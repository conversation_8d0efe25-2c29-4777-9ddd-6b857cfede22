# 🌟 Aniote - Anime Quote Telegram Channel Bot

An automated Telegram channel bot that posts inspiring anime quotes with AI-enhanced content using Google Gemini.

## 📋 Features

### ✅ Current Features
- **Automated Daily Posts**: 3 posts per day at optimal times (8 AM, 2 PM, 7 PM)
- **Smart Quote Selection**: Avoids repeating quotes for 30 days
- **AI-Enhanced Content**: Uses Google Gemini for hashtags and categorization
- **Time-Based Categories**: Different quote types for different times of day
- **Character Spotlights**: Weekly character features
- **Text-Based Posts**: Clean, readable format without images
- **Database Tracking**: SQLite database for analytics and post history

### 🔄 Content Types
1. **Daily Quotes**: Regular motivational/philosophical quotes
2. **Character Spotlights**: Weekly character features (Sundays)
3. **Category-Based Posts**: Motivation, wisdom, philosophy, love, friendship
4. **Manual Posts**: On-demand quote posting

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Environment Setup
Your `.env` file is already configured with:
- Telegram Bot Token
- Telegram Channel ID  
- Gemini API Key

### 3. Test the Bot
```bash
python scripts/test_bot.py
```

### 4. Start Automated Posting
```bash
python app.py
```

## 📊 Dataset
- **8,336 anime quotes** from various series
- Includes: Quote text, Character, Anime, Quote length
- Covers popular series like Naruto, One Piece, Attack on Titan, etc.

## 🤖 Bot Commands & Usage

### Testing Commands
```python
# Test basic functionality
python scripts/test_bot.py

# Manual quote posting
await bot.post_random_quote()
await bot.post_random_quote("motivation")
```

### Automated Schedule
- **8:00 AM**: Morning motivation quotes
- **2:00 PM**: Afternoon wisdom quotes  
- **7:00 PM**: Evening philosophical quotes
- **Sundays 10:00 AM**: Character spotlights

## 📁 Project Structure

```
aniote/
├── src/
│   ├── core/
│   │   ├── bot.py              # Main bot logic
│   │   └── scheduler.py        # Automated posting
│   ├── utils/
│   │   ├── gemini_client.py    # AI content enhancement
│   │   └── helpers.py          # Utility functions
│   └── data/
│       └── quote_manager.py    # Quote database management
├── config/
│   └── settings.py             # Configuration
├── scripts/
│   └── test_bot.py            # Testing script
├── data/
│   ├── anime-quote.csv        # Quote dataset
│   └── quotes.db              # SQLite database (auto-created)
├── logs/                      # Log files (auto-created)
├── requirements.txt
├── .env                       # Environment variables
└── app.py                     # Main entry point
```

## 🎯 Content Strategy

### Post Formats
```
🌅 Good Morning!

💫 "Even if we walk on different paths, one must always live on as you are able!"

— **Erza Scarlet** from *Fairy Tail*

#AnimeQuotes #Motivation #FairyTail #NeverGiveUp #AnimeWisdom
```

### Character Spotlight Format
```
🌟 Character Spotlight 🌟

**Erza Scarlet** from *Fairy Tail*

A powerful S-Class mage known for her strict discipline and unwavering determination...

💫 "Even if we walk on different paths, one must always live on as you are able!"

#CharacterSpotlight #FairyTail #ErzaScarlet #AnimeCharacters
```

## 🔧 Configuration

### Posting Schedule
Edit `config/settings.py` to modify:
- Posting times
- Quote length limits
- Hashtag settings
- Database paths

### Content Categories
- **Morning**: Motivation, determination
- **Afternoon**: Wisdom, life lessons
- **Evening**: Philosophy, reflection

## 📈 Analytics & Tracking

The bot automatically tracks:
- Posted quotes (prevents duplicates)
- Posting times and frequency
- Quote categories and performance
- Character spotlight history

## 🛠️ Development

### Adding New Features
1. Follow the modular structure in `src/`
2. Add tests in `src/tests/`
3. Update configuration in `config/settings.py`
4. Document changes in README.md

### Testing
```bash
# Run specific tests
python -m pytest src/tests/

# Test bot functionality
python scripts/test_bot.py
```

## 📝 Logs

Logs are automatically created in `logs/aniote.log` with:
- Colored console output
- File logging for debugging
- Error tracking and monitoring

## 🔮 Future Enhancements

### Planned Features
- **Character Images**: Download and use character images for spotlights
- **User Interaction**: Respond to comments and reactions
- **Advanced Analytics**: Engagement tracking and optimization
- **Custom Categories**: User-defined quote categories
- **Multi-language**: Support for different languages
- **Web Dashboard**: Analytics and control panel

### Character Image Integration
When ready to add images:
1. Character image downloading system
2. Image processing and optimization
3. Template-based image generation
4. Character spotlight visual cards

## 🤝 Contributing

1. Follow the coding guidelines in the project
2. Add proper logging and error handling
3. Include tests for new features
4. Update documentation

## 📄 License

This project is for educational and personal use. Respect anime content copyrights and Telegram's terms of service.
