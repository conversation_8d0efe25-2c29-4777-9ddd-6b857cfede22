import google.generativeai as genai
import logging
import json
from config.settings import settings

logger = logging.getLogger(__name__)

class GeminiClient:
    """Client for interacting with Google Gemini AI"""
    
    def __init__(self):
        self.api_key = settings.GEMINI_API_KEY
        self._configure_client()
    
    def _configure_client(self):
        """Configure the Gemini client"""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-pro')
            logger.info("Gemini client configured successfully")
        except Exception as e:
            logger.error(f"Error configuring Gemini client: {e}")
            raise
    
    def generate_hashtags(self, quote_data):
        """Generate relevant hashtags for a quote"""
        try:
            quote = quote_data['Quote']
            character = quote_data['Character']
            anime = quote_data['Anime']
            
            prompt = f"""
            Generate 5-8 relevant hashtags for this anime quote post on Telegram:
            
            Quote: "{quote}"
            Character: {character}
            Anime: {anime}
            
            Requirements:
            - Include anime/character specific hashtags
            - Include thematic hashtags based on quote content
            - Include general anime community hashtags
            - Keep hashtags concise and relevant
            - Format as a simple list separated by spaces
            - Start each hashtag with #
            
            Example format: #AnimeQuotes #Naruto #Motivation #Wisdom #AnimeWisdom
            """
            
            response = self.model.generate_content(prompt)
            hashtags_text = response.text.strip()
            
            # Parse hashtags
            hashtags = [tag.strip() for tag in hashtags_text.split() if tag.startswith('#')]
            
            # Ensure we have default hashtags
            if not hashtags:
                hashtags = settings.DEFAULT_HASHTAGS.copy()
            
            # Limit number of hashtags
            hashtags = hashtags[:settings.MAX_HASHTAGS_PER_POST]
            
            logger.info(f"Generated {len(hashtags)} hashtags for quote")
            return hashtags
            
        except Exception as e:
            logger.error(f"Error generating hashtags: {e}")
            return settings.DEFAULT_HASHTAGS.copy()
    
    def generate_quote_context(self, quote_data):
        """Generate contextual information about a quote"""
        try:
            quote = quote_data['Quote']
            character = quote_data['Character']
            anime = quote_data['Anime']
            
            prompt = f"""
            Provide a brief, engaging context or interpretation for this anime quote:
            
            Quote: "{quote}"
            Character: {character}
            Anime: {anime}
            
            Requirements:
            - Keep it under 100 words
            - Make it engaging and thoughtful
            - Relate it to real life or universal themes
            - Don't spoil the anime
            - Write in a conversational tone
            
            Format: Just return the context text, no extra formatting.
            """
            
            response = self.model.generate_content(prompt)
            context = response.text.strip()
            
            logger.info("Generated context for quote")
            return context
            
        except Exception as e:
            logger.error(f"Error generating quote context: {e}")
            return None
    
    def categorize_quote(self, quote_data):
        """Categorize a quote by theme"""
        try:
            quote = quote_data['Quote']
            
            prompt = f"""
            Categorize this anime quote into one primary category:
            
            Quote: "{quote}"
            
            Categories:
            - motivation
            - love
            - friendship
            - philosophy
            - wisdom
            - humor
            - sadness
            - determination
            - life_lessons
            
            Return only the category name, nothing else.
            """
            
            response = self.model.generate_content(prompt)
            category = response.text.strip().lower()
            
            # Validate category
            valid_categories = [
                'motivation', 'love', 'friendship', 'philosophy', 
                'wisdom', 'humor', 'sadness', 'determination', 'life_lessons'
            ]
            
            if category not in valid_categories:
                category = 'wisdom'  # Default category
            
            logger.info(f"Categorized quote as: {category}")
            return category
            
        except Exception as e:
            logger.error(f"Error categorizing quote: {e}")
            return 'wisdom'
    
    def generate_character_spotlight(self, character_name, anime_name, quotes_list):
        """Generate content for character spotlight feature"""
        try:
            quotes_text = "\n".join([f'- "{q["Quote"]}"' for q in quotes_list[:3]])
            
            prompt = f"""
            Create an engaging character spotlight post for:
            
            Character: {character_name}
            Anime: {anime_name}
            
            Sample quotes:
            {quotes_text}
            
            Requirements:
            - Write a brief character description (2-3 sentences)
            - Highlight their personality or role
            - Keep it spoiler-free
            - Make it engaging for anime fans
            - Under 150 words total
            
            Format: Just return the spotlight text, no extra formatting.
            """
            
            response = self.model.generate_content(prompt)
            spotlight = response.text.strip()
            
            logger.info(f"Generated character spotlight for {character_name}")
            return spotlight
            
        except Exception as e:
            logger.error(f"Error generating character spotlight: {e}")
            return None
    
    def enhance_post_content(self, quote_data, post_type="daily"):
        """Enhance post content with AI-generated elements"""
        try:
            hashtags = self.generate_hashtags(quote_data)
            category = self.categorize_quote(quote_data)
            
            enhancement = {
                'hashtags': hashtags,
                'category': category,
                'context': None
            }
            
            # Add context for special post types
            if post_type in ['featured', 'spotlight']:
                enhancement['context'] = self.generate_quote_context(quote_data)
            
            return enhancement
            
        except Exception as e:
            logger.error(f"Error enhancing post content: {e}")
            return {
                'hashtags': settings.DEFAULT_HASHTAGS.copy(),
                'category': 'wisdom',
                'context': None
            }
