import logging
import asyncio
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError
from config.settings import settings
from src.core.scheduler import PostScheduler

logger = logging.getLogger(__name__)

class AnimeQuoteBot:
    """Main bot class for the Anime Quote Telegram Channel"""
    
    def __init__(self):
        self.token = settings.TELEGRAM_BOT_TOKEN
        self.channel_id = settings.TELEGRAM_CHANNEL_ID
        self.bot = None
        self.scheduler = None
        self._validate_settings()
    
    def _validate_settings(self):
        """Validate bot settings"""
        try:
            settings.validate()
            logger.info("Bot settings validated successfully")
        except ValueError as e:
            logger.error(f"Invalid settings: {e}")
            raise
    
    async def initialize(self):
        """Initialize the bot and its components"""
        try:
            # Initialize Telegram bot
            self.bot = Bot(token=self.token)
            
            # Test bot connection
            bot_info = await self.bot.get_me()
            logger.info(f"Bot initialized: @{bot_info.username}")
            
            # Initialize scheduler
            self.scheduler = PostScheduler(self)
            logger.info("Bot components initialized successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing bot: {e}")
            return False
    
    async def post_to_channel(self, content, parse_mode='Markdown'):
        """Post content to the Telegram channel"""
        try:
            if not self.bot:
                logger.error("Bot not initialized")
                return False
            
            message = await self.bot.send_message(
                chat_id=self.channel_id,
                text=content,
                parse_mode=parse_mode
            )
            
            logger.info(f"Message posted successfully: {message.message_id}")
            return True
            
        except TelegramError as e:
            logger.error(f"Telegram error posting message: {e}")
            return False
        except Exception as e:
            logger.error(f"Error posting to channel: {e}")
            return False
    
    async def test_connection(self):
        """Test the bot connection and channel access"""
        try:
            if not self.bot:
                await self.initialize()
            
            # Test bot info
            bot_info = await self.bot.get_me()
            logger.info(f"Bot connection test successful: @{bot_info.username}")
            
            # Test channel access by getting chat info
            try:
                chat_info = await self.bot.get_chat(self.channel_id)
                logger.info(f"Channel access test successful: {chat_info.title}")
            except TelegramError as e:
                logger.warning(f"Channel access test failed: {e}")
                logger.info("This might be normal if the bot hasn't been added to the channel yet")
            
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    async def post_test_message(self):
        """Post a test message to verify everything works"""
        test_content = """🤖 **Anime Quote Bot Test**

"The only one who can decide your worth is you."

— **Jabami Yumeko** from *Kakegurui*

#AnimeQuotes #TestPost #BotOnline"""
        
        success = await self.post_to_channel(test_content)
        if success:
            logger.info("Test message posted successfully")
        else:
            logger.error("Failed to post test message")
        
        return success
    
    async def start_automated_posting(self):
        """Start the automated posting system"""
        try:
            if not self.scheduler:
                logger.error("Scheduler not initialized")
                return False
            
            logger.info("Starting automated posting system")
            await self.scheduler.start_scheduler()
            
        except Exception as e:
            logger.error(f"Error starting automated posting: {e}")
            return False
    
    async def post_random_quote(self, category=None):
        """Post a random quote immediately"""
        if not self.scheduler:
            logger.error("Scheduler not initialized")
            return False
        
        return await self.scheduler.post_random_quote(category)
    
    async def start(self):
        """Start the bot"""
        try:
            logger.info("Starting Anime Quote Bot")
            
            # Initialize bot
            if not await self.initialize():
                logger.error("Failed to initialize bot")
                return
            
            # Test connection
            if not await self.test_connection():
                logger.error("Connection test failed")
                return
            
            logger.info("Bot started successfully")
            logger.info("Use the following commands:")
            logger.info("- Post test message: await bot.post_test_message()")
            logger.info("- Post random quote: await bot.post_random_quote()")
            logger.info("- Start automated posting: await bot.start_automated_posting()")
            
            # Keep the bot running
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error running bot: {e}")
        finally:
            if self.scheduler:
                self.scheduler.stop_scheduler()
            logger.info("Bot shutdown complete")
    
    async def shutdown(self):
        """Gracefully shutdown the bot"""
        logger.info("Shutting down bot")
        if self.scheduler:
            self.scheduler.stop_scheduler()
        logger.info("Bot shutdown complete")
