2025-06-06 00:43:02 - src.core.bot - INFO - <PERSON><PERSON> settings validated successfully
2025-06-06 00:43:04 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7851583679:AAEOB1YF4iiQ4SVebDu2HfuLOK1YDcsMLz4/getMe "HTTP/1.1 200 OK"
2025-06-06 00:43:04 - src.core.bot - INFO - Bot initialized: @SophiaInsightBot
2025-06-06 00:43:04 - src.data.quote_manager - INFO - Database initialized successfully
2025-06-06 00:43:05 - src.data.quote_manager - INFO - Loaded 8334 quotes from CSV
2025-06-06 00:43:05 - src.data.quote_manager - INFO - After cleaning: 8330 valid quotes
2025-06-06 00:43:05 - src.utils.gemini_client - INFO - Gemini client configured successfully
2025-06-06 00:43:05 - src.core.scheduler - INFO - Scheduled daily post at 08:00
2025-06-06 00:43:05 - src.core.scheduler - INFO - Scheduled daily post at 14:00
2025-06-06 00:43:05 - src.core.scheduler - INFO - Scheduled daily post at 19:00
2025-06-06 00:43:05 - src.core.scheduler - INFO - Scheduled weekly character spotlight
2025-06-06 00:43:05 - src.core.scheduler - INFO - Post schedule configured successfully
2025-06-06 00:43:05 - src.core.bot - INFO - Bot components initialized successfully
2025-06-06 00:43:05 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7851583679:AAEOB1YF4iiQ4SVebDu2HfuLOK1YDcsMLz4/getMe "HTTP/1.1 200 OK"
2025-06-06 00:43:05 - src.core.bot - INFO - Bot connection test successful: @SophiaInsightBot
2025-06-06 00:43:05 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7851583679:AAEOB1YF4iiQ4SVebDu2HfuLOK1YDcsMLz4/getChat "HTTP/1.1 400 Bad Request"
2025-06-06 00:43:05 - src.core.bot - WARNING - Channel access test failed: Chat not found
2025-06-06 00:43:05 - src.core.bot - INFO - This might be normal if the bot hasn't been added to the channel yet
2025-06-06 00:43:18 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7851583679:AAEOB1YF4iiQ4SVebDu2HfuLOK1YDcsMLz4/sendMessage "HTTP/1.1 400 Bad Request"
2025-06-06 00:43:18 - src.core.bot - ERROR - Telegram error posting message: Chat not found
2025-06-06 00:43:18 - src.core.bot - ERROR - Failed to post test message
2025-06-06 00:43:18 - src.core.bot - INFO - Shutting down bot
2025-06-06 00:43:18 - src.core.scheduler - INFO - Stopping post scheduler
2025-06-06 00:43:18 - src.core.bot - INFO - Bot shutdown complete
2025-06-06 00:50:35 - src.core.bot - INFO - Bot settings validated successfully
2025-06-06 00:50:42 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/getMe "HTTP/1.1 200 OK"
2025-06-06 00:50:42 - src.core.bot - INFO - Bot initialized: @Aniote_Bot
2025-06-06 00:50:42 - src.data.quote_manager - INFO - Database initialized successfully
2025-06-06 00:50:42 - src.data.quote_manager - INFO - Loaded 8334 quotes from CSV
2025-06-06 00:50:42 - src.data.quote_manager - INFO - After cleaning: 8330 valid quotes
2025-06-06 00:50:42 - src.utils.gemini_client - INFO - Gemini client configured successfully
2025-06-06 00:50:42 - src.core.scheduler - INFO - Scheduled daily post at 08:00
2025-06-06 00:50:42 - src.core.scheduler - INFO - Scheduled daily post at 14:00
2025-06-06 00:50:42 - src.core.scheduler - INFO - Scheduled daily post at 19:00
2025-06-06 00:50:42 - src.core.scheduler - INFO - Scheduled weekly character spotlight
2025-06-06 00:50:42 - src.core.scheduler - INFO - Post schedule configured successfully
2025-06-06 00:50:42 - src.core.bot - INFO - Bot components initialized successfully
2025-06-06 00:50:43 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/getMe "HTTP/1.1 200 OK"
2025-06-06 00:50:43 - src.core.bot - INFO - Bot connection test successful: @Aniote_Bot
2025-06-06 00:50:45 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/getChat "HTTP/1.1 200 OK"
2025-06-06 00:50:45 - src.core.bot - INFO - Channel access test successful: Aniote
2025-06-06 00:50:50 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/sendMessage "HTTP/1.1 200 OK"
2025-06-06 00:50:50 - src.core.bot - INFO - Message posted successfully: 2
2025-06-06 00:50:50 - src.core.bot - INFO - Test message posted successfully
2025-06-06 00:50:50 - src.core.bot - INFO - Shutting down bot
2025-06-06 00:50:50 - src.core.scheduler - INFO - Stopping post scheduler
2025-06-06 00:50:50 - src.core.bot - INFO - Bot shutdown complete
2025-06-06 00:50:59 - src.core.bot - INFO - Bot settings validated successfully
2025-06-06 00:51:02 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/getMe "HTTP/1.1 200 OK"
2025-06-06 00:51:02 - src.core.bot - INFO - Bot initialized: @Aniote_Bot
2025-06-06 00:51:02 - src.data.quote_manager - INFO - Database initialized successfully
2025-06-06 00:51:02 - src.data.quote_manager - INFO - Loaded 8334 quotes from CSV
2025-06-06 00:51:02 - src.data.quote_manager - INFO - After cleaning: 8330 valid quotes
2025-06-06 00:51:02 - src.utils.gemini_client - INFO - Gemini client configured successfully
2025-06-06 00:51:02 - src.core.scheduler - INFO - Scheduled daily post at 08:00
2025-06-06 00:51:02 - src.core.scheduler - INFO - Scheduled daily post at 14:00
2025-06-06 00:51:02 - src.core.scheduler - INFO - Scheduled daily post at 19:00
2025-06-06 00:51:02 - src.core.scheduler - INFO - Scheduled weekly character spotlight
2025-06-06 00:51:02 - src.core.scheduler - INFO - Post schedule configured successfully
2025-06-06 00:51:02 - src.core.bot - INFO - Bot components initialized successfully
2025-06-06 00:51:03 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/getMe "HTTP/1.1 200 OK"
2025-06-06 00:51:03 - src.core.bot - INFO - Bot connection test successful: @Aniote_Bot
2025-06-06 00:51:03 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/getChat "HTTP/1.1 200 OK"
2025-06-06 00:51:03 - src.core.bot - INFO - Channel access test successful: Aniote
2025-06-06 00:51:09 - src.core.scheduler - INFO - Posting random quote (category: philosophy)
2025-06-06 00:51:11 - src.utils.gemini_client - ERROR - Error generating hashtags: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-06-06 00:51:12 - src.utils.gemini_client - ERROR - Error categorizing quote: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-06-06 00:51:14 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7399046433:AAFUWIoWXiYy3pb8MMoTTY5j6NIlSe9XzuU/sendMessage "HTTP/1.1 200 OK"
2025-06-06 00:51:14 - src.core.bot - INFO - Message posted successfully: 3
2025-06-06 00:51:14 - src.data.quote_manager - INFO - Marked quote 2544 as posted
2025-06-06 00:51:14 - src.core.scheduler - INFO - Successfully posted random quote: 2544
2025-06-06 00:51:14 - src.core.bot - INFO - Shutting down bot
2025-06-06 00:51:14 - src.core.scheduler - INFO - Stopping post scheduler
2025-06-06 00:51:14 - src.core.bot - INFO - Bot shutdown complete
