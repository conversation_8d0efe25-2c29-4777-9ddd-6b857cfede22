#!/usr/bin/env python3
"""
Test script for the Anime Quote Bot
Run this to test basic functionality before starting automated posting
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.bot import AnimeQuoteBot
from src.utils.helpers import setup_logging

async def test_bot():
    """Test the bot functionality"""
    
    # Setup logging
    setup_logging()
    
    print("🤖 Testing Anime Quote Bot...")
    print("=" * 50)
    
    try:
        # Initialize bot
        bot = AnimeQuoteBot()
        
        print("✅ Bot instance created")
        
        # Initialize components
        if await bot.initialize():
            print("✅ Bot initialized successfully")
        else:
            print("❌ Bot initialization failed")
            return
        
        # Test connection
        if await bot.test_connection():
            print("✅ Connection test passed")
        else:
            print("❌ Connection test failed")
            return
        
        # Ask user what to test
        print("\nWhat would you like to test?")
        print("1. Post test message")
        print("2. Post random quote")
        print("3. Post motivational quote")
        print("4. Post philosophical quote")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            print("\n📤 Posting test message...")
            if await bot.post_test_message():
                print("✅ Test message posted successfully!")
            else:
                print("❌ Failed to post test message")
        
        elif choice == "2":
            print("\n📤 Posting random quote...")
            if await bot.post_random_quote():
                print("✅ Random quote posted successfully!")
            else:
                print("❌ Failed to post random quote")
        
        elif choice == "3":
            print("\n📤 Posting motivational quote...")
            if await bot.post_random_quote("motivation"):
                print("✅ Motivational quote posted successfully!")
            else:
                print("❌ Failed to post motivational quote")
        
        elif choice == "4":
            print("\n📤 Posting philosophical quote...")
            if await bot.post_random_quote("philosophy"):
                print("✅ Philosophical quote posted successfully!")
            else:
                print("❌ Failed to post philosophical quote")
        
        elif choice == "5":
            print("👋 Exiting...")
        
        else:
            print("❌ Invalid choice")
        
        # Shutdown
        await bot.shutdown()
        print("\n✅ Bot test completed")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_bot())
